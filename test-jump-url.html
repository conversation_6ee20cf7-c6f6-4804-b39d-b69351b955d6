<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jump URL Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .vue-button {
            background-color: #4CAF50;
        }
        .vue-button:hover {
            background-color: #45a049;
        }
        .info {
            background-color: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #ffe7e7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            color: #d00;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Jump URL 测试页面</h1>
        
        <div class="info">
            <strong>说明：</strong>这个页面用于测试不同路由下的跳转URL配置是否正确。
        </div>
        
        <div class="test-section">
            <h3>测试链接</h3>
            <p>点击下面的链接测试不同路由的跳转功能：</p>
            
            <a href="http://localhost:81/?order_id=test123&user_id=user456&sign=testsign&timestamp=1691740800&pay_channel=gcash&terminal=1" target="_blank">
                <button class="button">测试 Home 路由 (Cocos)</button>
            </a>
            
            <a href="http://localhost:81/cocos/payResult?order_id=test123&user_id=user456&sign=testsign&timestamp=1691740800&pay_channel=gcash&terminal=1" target="_blank">
                <button class="button">测试 Cocos 路由</button>
            </a>
            
            <a href="http://localhost:81/vueH5/payResult?order_id=test123&user_id=user456&sign=testsign&timestamp=1691740800&pay_channel=gcash&terminal=1" target="_blank">
                <button class="vue-button">测试 Vue H5 路由</button>
            </a>
        </div>
        
        <div class="test-section">
            <h3>环境变量配置</h3>
            <p>当前开发环境的配置：</p>
            <ul>
                <li><strong>VITE_JUMP_URL:</strong> https://dev-h5.nustaronline.vip/?debug_web_login=1</li>
                <li><strong>VITE_JUMP_URL_VUE:</strong> https://demo.dabet666.ph</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>预期行为</h3>
            <ul>
                <li><strong>Home 和 Cocos 路由：</strong> 应该使用 VITE_JUMP_URL</li>
                <li><strong>Vue H5 路由：</strong> 应该使用 VITE_JUMP_URL_VUE</li>
                <li><strong>控制台输出：</strong> 查看浏览器控制台的调试信息</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>故障排除</h3>
            <p>如果跳转URL仍然为空，请检查：</p>
            <ol>
                <li>环境变量是否正确配置（去除注释 #TODO）</li>
                <li>开发服务器是否重启以加载新的环境变量</li>
                <li>浏览器控制台是否有错误信息</li>
                <li>路由名称是否正确匹配</li>
            </ol>
        </div>
    </div>
</body>
</html>
