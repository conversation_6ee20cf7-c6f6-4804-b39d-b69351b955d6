<script setup lang="ts" name="Landing">
import { ref, onMounted, computed, onBeforeMount } from "vue";
import { getPayStatus } from "@/service";
import Loading from "@/components/Loading.vue";
import { getJumpURL } from "@/utils/env";
import successPng from "@/assets/img/success.png";
import unsuccessPng from "@/assets/img/unSuccessful.png";
import pendingPng from "@/assets/img/pending.png";
import { useRoute } from "vue-router";

const PAGE_CONTENT = {
  0: {
    buttonText: "Back to Nustar",
    title: "Failed",
    img: unsuccessPng,
    titleColor: "#FF5353",
    urlParam: "type=pay&id=failed",
    extraContent: `You have successfully made a deposit.\n Good luck!`,
  },
  1: {
    buttonText: "Go Play",
    title: "Success",
    img: successPng,
    imgStyle: { width: "266px", height: "auto" },
    titleColor: "#01D46A",
    urlParam: "type=pay&id=success",
    extraContent: `You have successfully made a deposit.\n Good luck!`,
  },
  2: {
    buttonText: "Back to Nustar",
    title: "Unsuccessful",
    img: unsuccessPng,
    titleColor: "#FF5353",
    urlParam: "type=pay&id=failed",
    extraContent:
      "Your deposit could not be completed due to a system error. Please try again later.",
  },
  3: {
    buttonText: "Back to Nustar",
    title: "Pending",
    img: pendingPng,
    titleColor: "#5690FF",
    urlParam: "type=pay&id=pending",
    extraContent: `Your deposit request is currently being processed.\n Please kindly wait.`,
  },
  4: {
    buttonText: "Back to Nustar",
    title: "Canceled",
    img: unsuccessPng,
    titleColor: "#FF5353",
    urlParam: "type=pay&id=canceled",
    extraContent:
      "Your deposit could not be completed due to a system error. Please try again later.",
  },
};

const ROUTE_NAMES = ["home", "cocosPayResult", "vueH5PayResult"];

const route = useRoute();
const { name, query } = route;

const payStatus = ref<{
  status: number; // 1 成功 其他 失败
  content: string; // 提示信息
  urlParam: string;
  amount: string | number;
}>();
const terminal = ref(Number(query.terminal as string));

const loading = ref(false);

/**
 * 初始化支付结果页面
 * 获取支付状态并设置页面内容
 */
async function init() {
  loading.value = true;

  try {
    // 提取并验证必需的查询参数
    const requiredParams = {
      order_id: query.order_id as string,
      user_id: query.user_id as string,
      sign: query.sign as string,
      timestamp: query.timestamp as string,
      pay_channel: query.pay_channel as string,
    };

    // 验证必需参数是否存在
    const missingParams = Object.entries(requiredParams)
      .filter(([_, value]) => !value)
      .map(([key]) => key);

    if (missingParams.length > 0) {
      console.warn("Missing required parameters:", missingParams);
      setErrorState("Missing required parameters");
      return;
    }

    // 调用API获取支付状态
    const response = await getPayStatus(requiredParams);

    // 验证响应数据
    if (!response?.data?.status) {
      console.error("Invalid response data:", response);
      setErrorState("Invalid response data");
      return;
    }

    // 合并API响应和页面配置
    const statusConfig = PAGE_CONTENT[response.data.status];
    if (!statusConfig) {
      console.warn("Unknown payment status:", response.data.status);
      setErrorState("Unknown payment status");
      return;
    }

    payStatus.value = {
      ...response.data,
      ...statusConfig,
    };
  } catch (error) {
    console.error("Failed to get payment status:", error);
    setErrorState("Failed to retrieve payment status");
  } finally {
    loading.value = false;
  }
}

/**
 * 设置错误状态
 * @param message 错误信息
 */
function setErrorState(message: string) {
  payStatus.value = {
    status: 0,
    content: message,
    amount: 0,
    ...PAGE_CONTENT[0], // 使用失败状态的配置
  };
}
//关闭页面
function closePage() {
  if (navigator.userAgent.indexOf("Firefox") != -1 || navigator.userAgent.indexOf("Chrome") != -1) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}

// 充值失败跳转GCash-h5充值页面；充值成功跳转游戏history页面
function handleClick() {
  try {
    // 区分来源，ns-vue项目还是cocos的
    const isVue = name === ROUTE_NAMES[2];
    const jumpURL = getJumpURL(isVue);

    console.log("handleClick debug info:", {
      routeName: name,
      routeNames: ROUTE_NAMES,
      isVue,
      jumpURL,
      payStatus: payStatus.value,
      query,
    });

    if (!jumpURL) {
      console.error("Jump URL is empty, cannot proceed with navigation");
      return;
    }

    const hasParams = jumpURL.indexOf("?") > -1;
    const timestamp = query.timestamp as string; // 跳转给cocoS页面使用，判断刷新key值
    const newUrl =
      jumpURL +
      (hasParams ? "&" : "/?") +
      payStatus.value?.urlParam +
      "&amount=" +
      payStatus.value?.amount +
      "&timestamp=" +
      timestamp;

    console.log("Navigating to:", newUrl);
    window.location.href = newUrl;
  } catch (error) {
    console.error("Error in handleClick:", error);
    alert("页面跳转失败，请重试");
  }
}

// onBeforeMount(() => {
init();
// });
</script>

<template>
  <Loading v-if="loading" />
  <div class="container" v-else>
    <div class="content">
      <img class="icon" :src="payStatus?.img" :style="payStatus?.imgStyle || {}" />
      <span :style="{ color: payStatus?.titleColor || '#5690FF' }">{{ payStatus?.title }}</span>
      <div class="bottom">
        {{ payStatus?.content || payStatus?.extraContent }}
      </div>
    </div>

    <div v-if="payStatus?.buttonText" class="button" @click="handleClick">
      {{ payStatus?.buttonText }}
    </div>
  </div>
  <!--  <div class="container" v-else>
    <div class="content">
      <img class="icon" src="@/assets/img/unSuccessful.png" />
      <span>Failed</span>
      <div class="bottom">Error Request</div>
    </div>
    <div class="button" @click="handleClick">Back to Nustar</div>
  </div> -->
</template>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  height: 100vh;
  height: 100dvh;

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 700;
    line-height: 48px;
    color: #ff5353;

    .icon {
      margin-bottom: 30px;
      width: 148px;
      height: auto;
    }
  }

  .bottom {
    white-space: pre-line;
    padding: 0 20px 60px;
    width: 100%;
    color: #8d949d;
    font-size: 14px;
    font-family: "Inter";
    font-weight: 400;
    line-height: 28px;
    letter-spacing: 0%;
    white-space: pre-line;
  }

  .button {
    width: 80%;
    margin: 30px auto;
    background-color: #2b89f6;
    color: #fff;
    height: 48px;
    border-radius: 48px;
    line-height: 48px;
    font-size: 16px;
    font-weight: 800;
    color: #fff;
    background-color: #ac1140;
  }

  .demo-link {
    text-align: center;
    margin: 20px auto;
    padding: 10px;

    a {
      display: inline-block;
      padding: 8px 16px;
      border: 1px solid #2b89f6;
      border-radius: 20px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #2b89f6;
        color: white !important;
        transform: translateY(-1px);
      }
    }
  }
}
</style>
